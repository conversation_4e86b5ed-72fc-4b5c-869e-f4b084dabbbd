import { supabase } from "@/integrations/supabase/client";

/**
 * Debug utilities for Supabase connection and query testing
 * These functions help identify and resolve database issues
 */

export const debugSupabase = {
  /**
   * Test basic Supabase connection
   */
  async testConnection() {
    console.log("🔍 Testing Supabase connection...");
    
    try {
      // Test basic connection with a simple query
      const { data, error, status, statusText } = await supabase
        .from("products")
        .select("count", { count: "exact", head: true });

      console.log("Connection test result:", {
        status,
        statusText,
        error: error?.message,
        count: data
      });

      if (error) {
        console.error("❌ Connection test failed:", error);
        return { success: false, error: error.message };
      }

      console.log("✅ Connection test successful");
      return { success: true };
    } catch (error) {
      console.error("❌ Connection test failed with exception:", error);
      return { success: false, error: String(error) };
    }
  },

  /**
   * Test NULL comparison queries (the main issue we're fixing)
   */
  async testNullComparisons() {
    console.log("🔍 Testing NULL comparisons...");
    
    try {
      // Test the corrected .is() method for NULL comparisons
      const { data: products, error: productsError } = await supabase
        .from("products")
        .select("id, name, deleted_at")
        .is("deleted_at", null)
        .limit(5);

      if (productsError) {
        console.error("❌ Products NULL comparison failed:", productsError);
        return { success: false, error: productsError.message };
      }

      console.log("✅ Products NULL comparison successful:", products);

      // Test users table as well
      const { data: users, error: usersError } = await supabase
        .from("users")
        .select("id, name, deleted_at")
        .is("deleted_at", null)
        .limit(5);

      if (usersError) {
        console.error("❌ Users NULL comparison failed:", usersError);
        return { success: false, error: usersError.message };
      }

      console.log("✅ Users NULL comparison successful:", users);

      return { 
        success: true, 
        data: { 
          products: products?.length || 0, 
          users: users?.length || 0 
        } 
      };
    } catch (error) {
      console.error("❌ NULL comparison test failed:", error);
      return { success: false, error: String(error) };
    }
  },

  /**
   * Test environment variables and configuration
   */
  testEnvironment() {
    console.log("🔍 Testing environment configuration...");
    
    const supabaseUrl = import.meta.env.VITE_SUPABASE_URL;
    const supabaseKey = import.meta.env.VITE_SUPABASE_ANON_KEY;

    console.log("Environment check:", {
      hasUrl: !!supabaseUrl,
      hasKey: !!supabaseKey,
      urlFormat: supabaseUrl ? (supabaseUrl.includes('supabase.co') ? 'valid' : 'invalid') : 'missing',
      keyLength: supabaseKey ? supabaseKey.length : 0
    });

    if (!supabaseUrl || !supabaseKey) {
      console.error("❌ Missing environment variables");
      return { 
        success: false, 
        error: "Missing VITE_SUPABASE_URL or VITE_SUPABASE_ANON_KEY" 
      };
    }

    console.log("✅ Environment configuration looks good");
    return { success: true };
  },

  /**
   * Run all debug tests
   */
  async runAllDebugTests() {
    console.log("🚀 Running all Supabase debug tests...");
    
    const results = {
      environment: this.testEnvironment(),
      connection: await this.testConnection(),
      nullComparisons: await this.testNullComparisons(),
    };

    console.log("📊 Debug Results Summary:", results);
    
    const allPassed = Object.values(results).every(result => result.success);
    
    if (allPassed) {
      console.log("🎉 All debug tests passed! Supabase should be working correctly.");
    } else {
      console.log("⚠️ Some debug tests failed. Check the results above for issues.");
    }
    
    return results;
  },
};

// Make it available globally for console debugging
if (typeof window !== 'undefined') {
  (window as any).debugSupabase = debugSupabase;
}
