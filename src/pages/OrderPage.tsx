import { useState } from "react";
import { FoodItem, OrderItem } from "@/types/food";
import { sideItems, mainItems } from "@/data/foodItems";
import { FoodItemCard } from "@/components/FoodItemCard";
import { OrderConfirmationDialog } from "@/components/OrderConfirmationDialog";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { orderService } from "@/services/orderService";
import { Package, Utensils, ShoppingCart } from "lucide-react";

export function OrderPage() {
  const [selectedSideItems, setSelectedSideItems] = useState<Set<string>>(new Set());
  const [selectedMainItems, setSelectedMainItems] = useState<Set<string>>(new Set());
  const [isDialogOpen, setIsDialogOpen] = useState(false);

  const handleItemSelection = (itemId: string, selected: boolean) => {
    const item = [...sideItems, ...mainItems].find(item => item.id === itemId);
    
    if (!item) return;

    if (item.type === 'side') {
      const newSelectedSides = new Set(selectedSideItems);
      if (selected) {
        newSelectedSides.add(itemId);
      } else {
        newSelectedSides.delete(itemId);
      }
      setSelectedSideItems(newSelectedSides);
    } else {
      const newSelectedMains = new Set(selectedMainItems);
      if (selected) {
        newSelectedMains.add(itemId);
      } else {
        newSelectedMains.delete(itemId);
      }
      setSelectedMainItems(newSelectedMains);
    }
  };

  const getSelectedItems = (): FoodItem[] => {
    const selectedItems: FoodItem[] = [];
    
    // Add selected side items
    selectedSideItems.forEach(id => {
      const item = sideItems.find(item => item.id === id);
      if (item) selectedItems.push(item);
    });
    
    // Add selected main items
    selectedMainItems.forEach(id => {
      const item = mainItems.find(item => item.id === id);
      if (item) selectedItems.push(item);
    });
    
    return selectedItems;
  };

  const selectedItems = getSelectedItems();
  const totalPrice = selectedItems.reduce((sum, item) => sum + item.price, 0);
  const hasSelection = selectedItems.length > 0;

  const handleConfirmOrder = async (customerName: string, items: OrderItem[], total: number) => {
    // The OrderConfirmationDialog now handles the async saving internally
    // This callback is kept for backward compatibility and form reset

    // Reset form
    setSelectedSideItems(new Set());
    setSelectedMainItems(new Set());
  };

  const formatPrice = (price: number) => `¥${price}`;

  return (
    <div className="min-h-screen bg-gradient-warm">
      <div className="container mx-auto px-4 py-6 space-y-6">
        {/* Header Section */}
        <div className="text-center space-y-4">
          <h1 className="text-3xl font-bold text-foreground">
            🍱 Đặt Cơm Hôm Nay
          </h1>
          <p className="text-muted-foreground">
            Chọn món ăn yêu thích và đặt cơm ngay!
          </p>
        </div>

        {/* Side Items Section */}
        <Card className="bg-gradient-card shadow-card">
          <CardHeader>
            <CardTitle className="flex items-center gap-3">
              <div className="bg-food-secondary/20 p-2 rounded-lg">
                <Package className="h-5 w-5 text-food-secondary" />
              </div>
              <span>📦 Món Phụ</span>
              <Badge variant="secondary" className="ml-auto">
                Chọn nhiều món
              </Badge>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {sideItems.map(item => (
                <FoodItemCard
                  key={item.id}
                  item={item}
                  isSelected={selectedSideItems.has(item.id)}
                  onSelectionChange={handleItemSelection}
                />
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Main Items Section */}
        <Card className="bg-gradient-card shadow-card">
          <CardHeader>
            <CardTitle className="flex items-center gap-3">
              <div className="bg-primary/20 p-2 rounded-lg">
                <Utensils className="h-5 w-5 text-primary" />
              </div>
              <span>🍱 Món Chính</span>
              <Badge variant="secondary" className="ml-auto">
                Chọn nhiều món
              </Badge>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {mainItems.map(item => (
                <FoodItemCard
                  key={item.id}
                  item={item}
                  isSelected={selectedMainItems.has(item.id)}
                  onSelectionChange={handleItemSelection}
                />
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Order Summary & Action */}
        {hasSelection && (
          <Card className="bg-gradient-card shadow-elegant animate-fade-in">
            <CardContent className="p-6">
              <div className="flex flex-col md:flex-row md:items-center justify-between gap-4">
                <div className="space-y-2">
                  <h3 className="font-semibold text-lg flex items-center gap-2">
                    <ShoppingCart className="h-5 w-5 text-primary" />
                    Đơn hàng của bạn
                  </h3>
                  <p className="text-muted-foreground">
                    {selectedItems.length} món đã chọn
                  </p>
                  <div className="text-2xl font-bold text-primary">
                    Tổng: {formatPrice(totalPrice)}
                  </div>
                </div>
                
                <Button
                  onClick={() => setIsDialogOpen(true)}
                  className="bg-gradient-primary hover:opacity-90 px-8 py-3 text-lg font-medium animate-bounce-gentle"
                  size="lg"
                >
                  Xác Nhận Đặt Món
                </Button>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Confirmation Dialog */}
        <OrderConfirmationDialog
          isOpen={isDialogOpen}
          onClose={() => setIsDialogOpen(false)}
          selectedItems={selectedItems}
          onConfirmOrder={handleConfirmOrder}
        />
      </div>
    </div>
  );
}
