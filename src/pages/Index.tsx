import { useState } from "react";
import { Header } from "@/components/Header";
import { OrderPage } from "@/pages/OrderPage";
import { DashboardPage } from "@/pages/DashboardPage";
import { OrderManagementPage } from "@/pages/OrderManagementPage";

const Index = () => {
  const [currentPage, setCurrentPage] = useState<'order' | 'dashboard' | 'management'>('order');

  const renderPage = () => {
    switch (currentPage) {
      case 'order':
        return <OrderPage />;
      case 'dashboard':
        return <DashboardPage />;
      case 'management':
        return <OrderManagementPage />;
      default:
        return <OrderPage />;
    }
  };

  return (
    <div className="min-h-screen bg-background">
      <Header currentPage={currentPage} onPageChange={setCurrentPage} />
      {renderPage()}
    </div>
  );
};

export default Index;
