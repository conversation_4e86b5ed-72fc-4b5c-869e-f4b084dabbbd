import { useState } from "react";
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON> } from "lucide-react";
import { Button } from "@/components/ui/button";

interface HeaderProps {
  currentPage: 'order' | 'dashboard' | 'management';
  onPageChange: (page: 'order' | 'dashboard' | 'management') => void;
}

export function Header({ currentPage, onPageChange }: HeaderProps) {
  const [isMenuOpen, setIsMenuOpen] = useState(false);

  const toggleMenu = () => setIsMenuOpen(!isMenuOpen);

  return (
    <header className="bg-gradient-primary shadow-elegant sticky top-0 z-50">
      <div className="container mx-auto px-4">
        <div className="flex items-center justify-between h-16">
          {/* Logo */}
          <div className="flex items-center space-x-3">
            <div className="bg-white/20 p-2 rounded-lg">
              <UtensilsCrossed className="h-6 w-6 text-white" />
            </div>
            <h1 className="text-white text-xl font-bold hidden sm:block">
              Đặt Cơm Công Ty
            </h1>
          </div>

          {/* Desktop Navigation */}
          <nav className="hidden md:flex space-x-2">
            <Button
              variant={currentPage === 'order' ? 'secondary' : 'ghost'}
              onClick={() => onPageChange('order')}
              className={currentPage === 'order' 
                ? 'bg-white/20 text-white hover:bg-white/30' 
                : 'text-white/80 hover:bg-white/10 hover:text-white'
              }
            >
              <UtensilsCrossed className="h-4 w-4 mr-2" />
              Đặt Cơm
            </Button>
            <Button
              variant={currentPage === 'dashboard' ? 'secondary' : 'ghost'}
              onClick={() => onPageChange('dashboard')}
              className={currentPage === 'dashboard' 
                ? 'bg-white/20 text-white hover:bg-white/30' 
                : 'text-white/80 hover:bg-white/10 hover:text-white'
              }
            >
              <BarChart3 className="h-4 w-4 mr-2" />
              Thống Kê
            </Button>
            <Button
              variant={currentPage === 'management' ? 'secondary' : 'ghost'}
              onClick={() => onPageChange('management')}
              className={currentPage === 'management' 
                ? 'bg-white/20 text-white hover:bg-white/30' 
                : 'text-white/80 hover:bg-white/10 hover:text-white'
              }
            >
              <ClipboardList className="h-4 w-4 mr-2" />
              Quản lý đơn đặt
            </Button>
          </nav>

          {/* Mobile Menu Button */}
          <Button
            variant="ghost"
            size="icon"
            onClick={toggleMenu}
            className="md:hidden text-white hover:bg-white/10"
          >
            {isMenuOpen ? (
              <X className="h-5 w-5" />
            ) : (
              <Menu className="h-5 w-5" />
            )}
          </Button>
        </div>

        {/* Mobile Navigation */}
        {isMenuOpen && (
          <div className="md:hidden border-t border-white/20 py-4 animate-fade-in">
            <nav className="flex flex-col space-y-2">
              <Button
                variant={currentPage === 'order' ? 'secondary' : 'ghost'}
                onClick={() => {
                  onPageChange('order');
                  setIsMenuOpen(false);
                }}
                className={`justify-start ${currentPage === 'order' 
                  ? 'bg-white/20 text-white hover:bg-white/30' 
                  : 'text-white/80 hover:bg-white/10 hover:text-white'
                }`}
              >
                <UtensilsCrossed className="h-4 w-4 mr-2" />
                Đặt Cơm
              </Button>
              <Button
                variant={currentPage === 'dashboard' ? 'secondary' : 'ghost'}
                onClick={() => {
                  onPageChange('dashboard');
                  setIsMenuOpen(false);
                }}
                className={`justify-start ${currentPage === 'dashboard' 
                  ? 'bg-white/20 text-white hover:bg-white/30' 
                  : 'text-white/80 hover:bg-white/10 hover:text-white'
                }`}
              >
                <BarChart3 className="h-4 w-4 mr-2" />
                Thống Kê
              </Button>
              <Button
                variant={currentPage === 'management' ? 'secondary' : 'ghost'}
                onClick={() => {
                  onPageChange('management');
                  setIsMenuOpen(false);
                }}
                className={`justify-start ${currentPage === 'management' 
                  ? 'bg-white/20 text-white hover:bg-white/30' 
                  : 'text-white/80 hover:bg-white/10 hover:text-white'
                }`}
              >
                <ClipboardList className="h-4 w-4 mr-2" />
                Quản lý đơn đặt
              </Button>
            </nav>
          </div>
        )}
      </div>
    </header>
  );
}