import { Order, OrderItem, DailyStats } from "@/types/food";
import { allFoodItems } from "@/data/foodItems";
import { supabaseOrderService } from "./supabaseOrderService";

const ORDERS_KEY = 'food_orders';

export const orderService = {
  // Save order to both Supabase and localStorage (for backward compatibility)
  saveOrder: async (customerName: string, items: OrderItem[], totalAmount: number): Promise<{ success: boolean; order?: Order; error?: string }> => {
    try {
      // Try to save to Supabase first
      const supabaseResult = await supabaseOrderService.saveOrderToDatabase(customerName, items, totalAmount);

      if (supabaseResult.success && supabaseResult.order) {
        // Also save to localStorage for backward compatibility
        const existingOrders = orderService.getAllOrders();
        const updatedOrders = [supabaseResult.order, ...existingOrders];
        localStorage.setItem(ORDERS_KEY, JSON.stringify(updatedOrders));

        return { success: true, order: supabaseResult.order };
      } else {
        // Fallback to localStorage only if Supabase fails
        console.warn("Supabase save failed, falling back to localStorage:", supabaseResult.error);

        const newOrder: Order = {
          id: `order_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
          customerName,
          items,
          totalAmount,
          createdAt: new Date(),
          isPaid: false
        };

        const existingOrders = orderService.getAllOrders();
        const updatedOrders = [newOrder, ...existingOrders];

        localStorage.setItem(ORDERS_KEY, JSON.stringify(updatedOrders));
        return { success: true, order: newOrder, error: `Saved to localStorage only: ${supabaseResult.error}` };
      }
    } catch (error) {
      console.error("Error in saveOrder:", error);

      // Fallback to localStorage
      const newOrder: Order = {
        id: `order_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
        customerName,
        items,
        totalAmount,
        createdAt: new Date(),
        isPaid: false
      };

      const existingOrders = orderService.getAllOrders();
      const updatedOrders = [newOrder, ...existingOrders];

      localStorage.setItem(ORDERS_KEY, JSON.stringify(updatedOrders));
      return { success: true, order: newOrder, error: `Saved to localStorage only: ${error}` };
    }
  },

  // Legacy synchronous method for backward compatibility
  saveOrderSync: (customerName: string, items: OrderItem[], totalAmount: number): Order => {
    const newOrder: Order = {
      id: `order_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
      customerName,
      items,
      totalAmount,
      createdAt: new Date(),
      isPaid: false
    };

    const existingOrders = orderService.getAllOrders();
    const updatedOrders = [newOrder, ...existingOrders];

    localStorage.setItem(ORDERS_KEY, JSON.stringify(updatedOrders));
    return newOrder;
  },

  // Get all orders from localStorage
  getAllOrders: (): Order[] => {
    const ordersJson = localStorage.getItem(ORDERS_KEY);
    if (!ordersJson) return [];
    
    return JSON.parse(ordersJson).map((order: any) => ({
      ...order,
      createdAt: new Date(order.createdAt)
    }));
  },

  // Get today's orders
  getTodayOrders: (): Order[] => {
    const allOrders = orderService.getAllOrders();
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    
    return allOrders.filter(order => {
      const orderDate = new Date(order.createdAt);
      orderDate.setHours(0, 0, 0, 0);
      return orderDate.getTime() === today.getTime();
    });
  },

  // Calculate daily statistics
  getDailyStats: (): DailyStats => {
    const todayOrders = orderService.getTodayOrders();
    
    const totalRevenue = todayOrders.reduce((sum, order) => sum + order.totalAmount, 0);
    const totalOrders = todayOrders.length;
    const paidOrders = todayOrders.filter(order => order.isPaid).length;
    const unpaidOrders = totalOrders - paidOrders;
    const totalDebt = todayOrders
      .filter(order => !order.isPaid)
      .reduce((sum, order) => sum + order.totalAmount, 0);

    // Calculate top items
    const itemCounts: { [key: string]: number } = {};
    todayOrders.forEach(order => {
      order.items.forEach(item => {
        const itemId = item.foodItem.id;
        itemCounts[itemId] = (itemCounts[itemId] || 0) + item.quantity;
      });
    });

    const topItems = Object.entries(itemCounts)
      .map(([itemId, quantity]) => ({
        item: allFoodItems.find(item => item.id === itemId)!,
        quantity
      }))
      .filter(entry => entry.item)
      .sort((a, b) => b.quantity - a.quantity)
      .slice(0, 5);

    return {
      totalRevenue,
      totalOrders,
      totalDebt,
      paidOrders,
      unpaidOrders,
      topItems
    };
  },

  // Toggle payment status
  togglePaymentStatus: (orderId: string): void => {
    const orders = orderService.getAllOrders();
    const updatedOrders = orders.map(order => 
      order.id === orderId 
        ? { ...order, isPaid: !order.isPaid }
        : order
    );
    
    localStorage.setItem(ORDERS_KEY, JSON.stringify(updatedOrders));
  },

  // Clear all orders (for testing)
  clearAllOrders: (): void => {
    localStorage.removeItem(ORDERS_KEY);
  }
};