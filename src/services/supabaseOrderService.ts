import { supabase } from "@/integrations/supabase/client";
import { Database, TablesInsert } from "@/integrations/supabase/types";
import { Order, OrderItem, FoodItem } from "@/types/food";

// Type aliases for better readability
type OrderInsert = TablesInsert<"orders">;
type OrderItemInsert = TablesInsert<"order_items">;
type UserInsert = TablesInsert<"users">;
type ProductInsert = TablesInsert<"products">;

export interface SaveOrderResult {
  success: boolean;
  order?: Order;
  error?: string;
}

export interface SupabaseOrderService {
  saveOrderToDatabase: (customerName: string, items: OrderItem[], totalAmount: number) => Promise<SaveOrderResult>;
  ensureUserExists: (userName: string) => Promise<{ success: boolean; userId?: string; error?: string }>;
  ensureProductsExist: (foodItems: FoodItem[]) => Promise<{ success: boolean; productMap?: Map<string, string>; error?: string }>;
  getOrderById: (orderId: string) => Promise<{ success: boolean; order?: any; error?: string }>;
}

export const supabaseOrderService: SupabaseOrderService = {
  /**
   * Ensures a user exists in the database, creates if not found
   */
  async ensureUserExists(userName: string) {
    try {
      // First, try to find existing user
      const { data: existingUser, error: findError } = await supabase
        .from("users")
        .select("id")
        .eq("name", userName)
        .eq("deleted_at", null)
        .single();

      if (findError && findError.code !== "PGRST116") {
        // PGRST116 is "not found" error, which is expected
        console.error("Error finding user:", findError);
        return { success: false, error: `Failed to find user: ${findError.message}` };
      }

      if (existingUser) {
        return { success: true, userId: existingUser.id };
      }

      // User doesn't exist, create new one
      const userInsert: UserInsert = {
        name: userName,
      };

      const { data: newUser, error: createError } = await supabase
        .from("users")
        .insert(userInsert)
        .select("id")
        .single();

      if (createError) {
        console.error("Error creating user:", createError);
        return { success: false, error: `Failed to create user: ${createError.message}` };
      }

      return { success: true, userId: newUser.id };
    } catch (error) {
      console.error("Unexpected error in ensureUserExists:", error);
      return { success: false, error: "Unexpected error occurred while managing user" };
    }
  },

  /**
   * Ensures products exist in the database, creates missing ones
   */
  async ensureProductsExist(foodItems: FoodItem[]) {
    try {
      const productMap = new Map<string, string>();

      // Get existing products
      const { data: existingProducts, error: fetchError } = await supabase
        .from("products")
        .select("id, name")
        .eq("deleted_at", null);

      if (fetchError) {
        console.error("Error fetching products:", fetchError);
        return { success: false, error: `Failed to fetch products: ${fetchError.message}` };
      }

      // Create a map of existing products by name
      const existingProductMap = new Map<string, string>();
      existingProducts?.forEach(product => {
        existingProductMap.set(product.name, product.id);
      });

      // Identify missing products
      const missingProducts: ProductInsert[] = [];
      
      for (const foodItem of foodItems) {
        if (existingProductMap.has(foodItem.name)) {
          productMap.set(foodItem.id, existingProductMap.get(foodItem.name)!);
        } else {
          missingProducts.push({
            name: foodItem.name,
            price: foodItem.price,
            category: foodItem.type as "main" | "side",
          });
        }
      }

      // Insert missing products if any
      if (missingProducts.length > 0) {
        const { data: newProducts, error: insertError } = await supabase
          .from("products")
          .insert(missingProducts)
          .select("id, name");

        if (insertError) {
          console.error("Error creating products:", insertError);
          return { success: false, error: `Failed to create products: ${insertError.message}` };
        }

        // Add new products to the map
        newProducts?.forEach(product => {
          const foodItem = foodItems.find(item => item.name === product.name);
          if (foodItem) {
            productMap.set(foodItem.id, product.id);
          }
        });
      }

      return { success: true, productMap };
    } catch (error) {
      console.error("Unexpected error in ensureProductsExist:", error);
      return { success: false, error: "Unexpected error occurred while managing products" };
    }
  },

  /**
   * Saves an order to the Supabase database
   */
  async saveOrderToDatabase(customerName: string, items: OrderItem[], totalAmount: number): Promise<SaveOrderResult> {
    try {
      // Step 1: Ensure user exists
      const userResult = await this.ensureUserExists(customerName);
      if (!userResult.success || !userResult.userId) {
        return { success: false, error: userResult.error || "Failed to create/find user" };
      }

      // Step 2: Ensure products exist
      const foodItems = items.map(item => item.foodItem);
      const productsResult = await this.ensureProductsExist(foodItems);
      if (!productsResult.success || !productsResult.productMap) {
        return { success: false, error: productsResult.error || "Failed to create/find products" };
      }

      // Step 3: Create order
      const orderInsert: OrderInsert = {
        user_id: userResult.userId,
        total_price: totalAmount,
        is_paid: false,
        order_date: new Date().toISOString(),
      };

      const { data: newOrder, error: orderError } = await supabase
        .from("orders")
        .insert(orderInsert)
        .select("*")
        .single();

      if (orderError) {
        console.error("Error creating order:", orderError);
        return { success: false, error: `Failed to create order: ${orderError.message}` };
      }

      // Step 4: Create order items
      const orderItemsInsert: OrderItemInsert[] = items.map(item => {
        const productId = productsResult.productMap!.get(item.foodItem.id);
        if (!productId) {
          throw new Error(`Product ID not found for food item: ${item.foodItem.name}`);
        }
        
        return {
          order_id: newOrder.id,
          product_id: productId,
          quantity: item.quantity,
        };
      });

      const { error: itemsError } = await supabase
        .from("order_items")
        .insert(orderItemsInsert);

      if (itemsError) {
        console.error("Error creating order items:", itemsError);
        // Try to clean up the order if order items failed
        await supabase.from("orders").delete().eq("id", newOrder.id);
        return { success: false, error: `Failed to create order items: ${itemsError.message}` };
      }

      // Step 5: Return success with order data
      const order: Order = {
        id: newOrder.id,
        customerName: customerName,
        items: items,
        totalAmount: totalAmount,
        createdAt: new Date(newOrder.created_at || new Date()),
        isPaid: newOrder.is_paid || false,
      };

      return { success: true, order };
    } catch (error) {
      console.error("Unexpected error in saveOrderToDatabase:", error);
      return { success: false, error: "Unexpected error occurred while saving order" };
    }
  },

  /**
   * Retrieves an order by ID from the database
   */
  async getOrderById(orderId: string) {
    try {
      const { data: order, error } = await supabase
        .from("orders")
        .select(`
          *,
          users!orders_user_id_fkey(name),
          order_items(
            quantity,
            products(id, name, price, category)
          )
        `)
        .eq("id", orderId)
        .eq("deleted_at", null)
        .single();

      if (error) {
        console.error("Error fetching order:", error);
        return { success: false, error: `Failed to fetch order: ${error.message}` };
      }

      return { success: true, order };
    } catch (error) {
      console.error("Unexpected error in getOrderById:", error);
      return { success: false, error: "Unexpected error occurred while fetching order" };
    }
  },
};
